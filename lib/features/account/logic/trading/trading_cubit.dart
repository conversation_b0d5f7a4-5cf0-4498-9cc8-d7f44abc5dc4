import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/trade.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';
import 'package:gp_stock_app/core/utils/fee_calculator.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_info/company_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_news/company_news_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/market_status/market_status_response.dart';
import 'package:gp_stock_app/features/account/domain/repository/account_repository.dart';
import 'package:gp_stock_app/features/account/domain/services/market_service.dart';
import 'package:gp_stock_app/features/market/domain/models/broker_queue/broker_queue.dart';
import 'package:gp_stock_app/features/market/domain/models/create_order_parameter/create_order_parameter.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/domain/models/tick_list_response.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/web_socket_extension.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/models/exchange_rate/exchange_rate.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';

import '../../../../shared/models/market_depth/market_depth.dart';
import '../../../market/domain/models/stock_reponse/stock_info_response.dart';
import '../../../market/domain/repositories/market_repository.dart';

part 'trading_state.dart';

class TradingCubit extends Cubit<TradingState> {
  final MarketRepository _marketService = getIt<MarketRepository>();
  final AccountRepository _accountService = getIt<AccountRepository>();
  final WebSocketService _webSocketService = getIt<WebSocketService>();

  TradingCubit() : super(const TradingState()) {
    _webSocketService.onMessageWithAction('Q', loginRequired: true).listen(_handleStockInfoUpdate);
    _webSocketService.onMessageWithAction('O', loginRequired: true).listen(_handleMarketDepthUpdate);
    _webSocketService.onMessageWithAction('T', loginRequired: true).listen(_handleTickData);
    // TODO: WebSocket action 'A' is not working currently. Needs backend update to implement this feature
    // _webSocketService.onMessageWithAction('A', loginRequired: true).listen((event) {});
  }

  void getMarketDepth(String instrument) async {
    emit(state.copyWith(marketDepthStatus: DataStatus.loading));
    final result = await _marketService.getMarketDepth(instrument);
    try {
      if (result.isSuccess) {
        emit(state.copyWith(
            marketDepthStatus: DataStatus.success,
            marketDepth: result.data?.data,
            marketDepthConstant: result.data?.data));
      } else {
        emit(state.copyWith(marketDepthStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(marketDepthStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void getStockInfo(
    String instrument, {
    VoidCallback? onStockInfoReceived,
  }) async {
    emit(state.copyWith(stockInfoStatus: DataStatus.loading));
    final result = await _marketService.getStockInfo(instrument);
    try {
      if (result.isSuccess) {
        emit(
          state.copyWith(
            stockInfoStatus: DataStatus.success,
            stockInfo: result.data?.data,
            stockInfoConstant: result.data?.data,
            limit: result.data?.data?.latestPrice ?? 0,
          ),
        );
        onStockInfoReceived?.call();
      } else {
        emit(state.copyWith(stockInfoStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(stockInfoStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void setStockInfo(StockInfoData? stockInfo) =>
      emit(state.copyWith(stockInfo: stockInfo, stockInfoConstant: stockInfo));

  /// Toggles between buy and sell trade directions and updates the order quantity accordingly.
  ///
  /// This method:
  /// 1. Sets the trade direction to the provided [tradeDirection]
  /// 2. Resets the order fraction to full (100%)
  /// 3. Recalculates the quantity based on the new direction and full fraction
  ///
  /// [tradeDirection] The trade direction to switch to (buy or sell)
  void toggleTradeDirection(TradeDirection tradeDirection) {
    final quantity = getQuantityByFraction(OrderFraction.full, tradeDirection: tradeDirection);
    emit(state.copyWith(
      tradeDirection: tradeDirection,
      orderFraction: () => OrderFraction.full,
      quantity: quantity,
      selectedPositionOpenShort: () => null,
      selectedPositionSellLong: () => null,
    ));
  }

  /// Increments the order quantity by the lot size and resets the order fraction to null since we're manually adjusting quantity.
  void incrementQuantity() => emit(state.copyWith(quantity: state.quantity + state.lotSize, orderFraction: () => null));

  /// Decrements the order quantity by the lot size and Resets the order fraction to null since we're manually adjusting quantity.
  void decrementQuantity() {
    if (state.quantity > state.lotSize) {
      emit(state.copyWith(quantity: state.quantity - state.lotSize, orderFraction: () => null));
    }
  }

  void setQuantity(double quantity) => emit(state.copyWith(quantity: quantity, orderFraction: () => null));

  /// Creates an order based on the provided [CreateOrderParameter].
  Future<bool> createOrder(
    CreateOrderParameter parameter, {
    required bool isBuyOrder,
  }) async {
    if ((isBuyOrder && state.createBuyOrderStatus == DataStatus.loading) ||
        (!isBuyOrder && state.createSellOrderStatus == DataStatus.loading)) {
      return false;
    }

    if (isBuyOrder) {
      emit(state.copyWith(createBuyOrderStatus: DataStatus.loading, createOrderStatus: DataStatus.loading));
    } else {
      emit(state.copyWith(createSellOrderStatus: DataStatus.loading, createOrderStatus: DataStatus.loading));
    }

    final result = await _marketService.createOrder(parameter);

    try {
      if (result.isSuccess) {
        if (isBuyOrder) {
          emit(state.copyWith(createBuyOrderStatus: DataStatus.success, createOrderStatus: DataStatus.success));
        } else {
          emit(state.copyWith(createSellOrderStatus: DataStatus.success, createOrderStatus: DataStatus.success));
        }
        return true;
      } else {
        if (isBuyOrder) {
          emit(state.copyWith(
              createBuyOrderStatus: DataStatus.failed, createOrderStatus: DataStatus.failed, error: result.error));
        } else {
          emit(state.copyWith(
              createSellOrderStatus: DataStatus.failed, createOrderStatus: DataStatus.failed, error: result.error));
        }
        return false;
      }
    } on Exception catch (e) {
      if (isBuyOrder) {
        emit(state.copyWith(
            createBuyOrderStatus: DataStatus.failed, createOrderStatus: DataStatus.failed, error: e.toString()));
      } else {
        emit(state.copyWith(
            createSellOrderStatus: DataStatus.failed, createOrderStatus: DataStatus.failed, error: e.toString()));
      }
      return false;
    }
  }

  /// Sets the price type for the order and updates the limit price.
  /// See [PriceType] for more information.
  void setPriceType(PriceType priceType) =>
      emit(state.copyWith(priceType: priceType, limit: state.stockInfo?.latestPrice ?? 0));

  /// Sets the contract and updates the account balance based on the contract's useAmount.
  /// This is applicable only if the trading method is contract-based. Not for spot trading.
  /// [contract] The contract summary data to set. Can be null to clear the contract.
  void setContract(ContractSummaryData? contract, {bool isFromContractDetails = false}) => emit(state.copyWith(
      contract: () => contract, accountBalance: contract?.useAmount, isFromContractDetails: isFromContractDetails));

  /// Sets the contract ID for the order.
  /// This is applicable only if the trading method is contract-based. Not for spot trading.
  ///  [contractId] The contract ID to set. Can be null to clear the contract ID.
  void setContractId(int? contractId) {
    emit(state.copyWith(contractId: () => contractId));
    Future.delayed(const Duration(milliseconds: 100), () {
      final quantity = getQuantityByFraction(OrderFraction.full, tradeDirection: state.tradeDirection);
      emit(state.copyWith(quantity: quantity));
    });
  }

  /// Increments the limit price by 0.001 (0.01 for A-shares) while handling decimal formatting based on locale
  /// A股使用两位小数（0.01），其他市场使用三位小数（0.001）
  /// Only applicable when [state.priceType] is [PriceType.limit]
  void incrementLimit({required MainMarketType marketType}) {
    // Gets the current decimal separator for the locale as it may be different for different locales
    // 获取当前语言环境的十进制分隔符，不同语言环境可能不同
    final decimalSeparator = NumberFormat().symbols.DECIMAL_SEP;
    // Formats the incremented value to 3 decimal places (default)
    // 格式化增量值，默认三位小数
    var formattedValue = NumberFormat("0.000").format(state.limit + 0.001);
    // A-shares use 2 decimal places with 0.01 increment
    // A股使用两位小数，增量为0.01
    if (marketType == MainMarketType.cnShares) {
      formattedValue = NumberFormat("0.00").format(state.limit + 0.01);
    }
    // Normalizes the decimal separator to '.' for parsing
    // 将十进制分隔符标准化为'.'以便解析
    final normalizedValue = decimalSeparator == ',' ? formattedValue.replaceAll(',', '.') : formattedValue;
    final newValue = double.parse(normalizedValue);
    emit(state.copyWith(limit: newValue));
  }

  /// Decrements the limit price by 0.001 (0.01 for A-shares) while handling decimal formatting based on locale
  /// A股使用两位小数（0.01），其他市场使用三位小数（0.001）
  /// Only applicable when [state.priceType] is [PriceType.limit]
  void decrementLimit({required MainMarketType marketType}) {
    if (state.limit > 0.001) {
      // Gets the current decimal separator for the locale as it may be different for different locales
      // 获取当前语言环境的十进制分隔符，不同语言环境可能不同
      final decimalSeparator = NumberFormat().symbols.DECIMAL_SEP;
      // Formats the decremented value to 3 decimal places (default)
      // 格式化减量值，默认三位小数
      var formattedValue = NumberFormat("0.000").format(state.limit - 0.001);
      // A-shares use 2 decimal places with 0.01 decrement
      // A股使用两位小数，减量为0.01
      if (marketType == MainMarketType.cnShares) {
        formattedValue = NumberFormat("0.00").format(state.limit - 0.01);
      }
      // Normalizes the decimal separator to '.' for parsing
      // 将十进制分隔符标准化为'.'以便解析
      final normalizedValue = decimalSeparator == ',' ? formattedValue.replaceAll(',', '.') : formattedValue;
      final newValue = double.parse(normalizedValue);
      emit(state.copyWith(limit: newValue));
    }
  }

  double calculateFee(double amount) {
    final isBuy = state.tradeDirection == TradeDirection.buy;
    final tradeNum = (state.isIndexTrading ? state.indexStockData?.tradeUnit : state.quantity) ?? 0;
    if (amount == 0) return 0;
    return FeeCalculator.calculateTradeHandlingFee(
      tradeAmount: amount,
      chargeList: isBuy ? state.calculateConfigBuy : state.calculateConfigSell,
      tradeNum: tradeNum,
      isStockIndex: state.isIndexTrading,
    );
    // if (state.isIndexTrading) {
    //   return (calculateIndexHandleFee(
    //     tradeAmount: amount,
    //     chargeList: isBuy ? state.calculateConfigBuy : state.calculateConfigSell,
    //     tradeUnit: state.indexStockData?.tradeUnit ?? 0,
    //   ));
    // } else {
    //   final feeRecord = calculateHandleFee(
    //     tradeAmount: amount,
    //     tradeNum: state.quantity,
    //     chargeList: isBuy ? state.calculateConfigBuy : state.calculateConfigSell,
    //   );
    //   return isBuy ? feeRecord.buyFee : feeRecord.sellFee;
    // }
  }

  /// Sets the limit price to the provided [limit].
  /// Only applicable when [state.priceType] is [PriceType.limit]
  void setLimit(double limit) => emit(state.copyWith(limit: limit));

  double _calculateTradingQuantity({
    required double accountBalance,
    required double fraction, // 比例：1/4、1/3、1/2、全仓
    required TradeDirection direction,
    required double stockPrice,
    int? tradeUnit,
    double availableQuantityToSell = 0,
  }) {
    if (state.tradingType == '' || accountBalance <= 0 || stockPrice <= 0 || state.lotSize <= 0) return 0;

    if (state.isIndexTrading) {}

    // double maxQtyEstimate = (accountBalance * fraction) / stockPrice;
    double maxQtyEstimate;
    if (direction == TradeDirection.buy) {
      // 买入：用余额 * fraction / 价格
      if (accountBalance <= 0) return 0;
      maxQtyEstimate = (accountBalance * fraction) / stockPrice;
    } else {
      // 卖出：基于可卖数量
      if (availableQuantityToSell <= 0) return 0;
      maxQtyEstimate = availableQuantityToSell * fraction;
      double roundedValue = ((maxQtyEstimate + (state.lotSize / 2)) ~/ state.lotSize) * state.lotSize;
      roundedValue = roundedValue == 0 ? state.lotSize : roundedValue;
      return roundedValue;
    }

    double left = 0;
    double right = maxQtyEstimate;
    double bestFit = 0;

    const double precision = 0.01;

    while ((right - left) > precision) {
      double mid = (left + right) / 2;
      double adjustedQty = (mid / state.lotSize).floorToDouble() * state.lotSize;
      final fee = calculateFee(accountBalance);
      double totalCost = (adjustedQty * stockPrice) + fee;
      if (totalCost <= accountBalance) {
        bestFit = adjustedQty;
        left = mid + precision;
      } else {
        right = mid - precision;
      }
    }

    return double.parse(bestFit.toStringAsFixed(2));
  }

  void setOrderFraction({OrderFraction? orderFraction, bool showToast = false}) {
    // If orderFraction is null, return without updating the state
    if (orderFraction == null) return;
    if (state.tradingType == '' && showToast) {
      GPEasyLoading.showToast('selectTradingType'.tr());
      return;
    } //Set available quantity to sell based on selected position
    // If no position selected, return without updating the state
    double availableQuantityToSell = 0;
    if (state.isIndexTrading && state.tradeDirection == TradeDirection.sell) {
      if (state.selectedPositionShort == null && state.selectedPositionLong == null) return;
      if (state.selectedPositionShort != null) {
        availableQuantityToSell = state.selectedPositionShort?.restNum ?? 0;
      } else if (state.selectedPositionLong != null) {
        availableQuantityToSell = state.selectedPositionLong?.restNum ?? 0;
      }
    } else if (state.tradeDirection == TradeDirection.sell) {
      availableQuantityToSell = state.maximumQuantityToSell;
    }

    /// 合约无需进行汇率转换
    bool contractSelected = state.contract != null;
    final quantity = _calculateTradingQuantity(
      accountBalance: contractSelected ? state.accountBalance : state.accountBalance * (state.exchangeRate?.rate ?? 1),
      fraction: orderFraction.fraction,
      direction: state.tradeDirection,
      stockPrice: state.stockInfoConstant?.latestPrice ?? 0,
      availableQuantityToSell: availableQuantityToSell,
      tradeUnit: state.indexStockData?.tradeUnit,
    );
    emit(state.copyWith(orderFraction: () => orderFraction, quantity: quantity.toDouble()));
  }

  /// Calculates the available trading quantity based on positions and trade direction.
  ///
  /// This function determines the available quantity for trading by considering:
  /// - Long and short positions in the portfolio
  /// - Current trade direction (buy/sell)
  /// - Market type (A-shares vs other markets)
  /// - Index trading status
  ///
  /// The calculation follows these rules:
  /// 1. For A-shares without index trading: Uses standard quantity calculation
  /// 2. For other markets or index trading:
  ///    - Buy direction: Uses account balance based calculation
  ///    - Sell direction: Uses available position quantities (long/short)
  ///
  /// Parameters:
  /// - [positions]: List of current trading positions
  /// - [isBuySection]: Flag indicating if this is for buy section calculation
  ///
  /// Returns the calculated available quantity for trading.
  double getAvailableQuantity(List<OrderRecord> positions, bool isBuySection) {
    if (state.tradingType == '') return 0;
    ({int long, int short}) getAvailableToClose(List<OrderRecord>? positions) {
      if (positions == null || positions.isEmpty) return (long: 0, short: 0);
      // Filter positions based on tradeType
      final longPositions = positions.where((p) => p.tradeType == 1).toList(); // Get long positions
      final shortPositions = positions.where((p) => p.tradeType == 2).toList(); // Get short positions
      final longSum =
          longPositions.fold(0, (sum, p) => sum + (p.restNum ?? 0).toInt()); // Calculate total long position
      final shortSum =
          shortPositions.fold(0, (sum, p) => sum + (p.restNum ?? 0).toInt()); // Calculate total short position
      return (long: longSum, short: shortSum);
    }

    // Initialize with full quantity fraction
    double available = getQuantityByFraction(OrderFraction.full);

    // Special handling for A-shares market
    if (getMainMarketType(state.stockInfoConstant?.market ?? '') == MainMarketType.cnShares && !state.isIndexTrading) {
      // A-shares logic handled by default available calculation
    } else {
      if (state.tradeDirection == TradeDirection.sell) {
        if (isBuySection) {
          // For buy section in sell direction, use short positions
          if (!state.isIndexTrading) {
            available = getAvailableToClose(positions).short.toDouble();
          } else {
            final restNum = state.selectedPositionLong?.restNum ?? 0;
            if (restNum > 0) {
              available = restNum;
            }
          }
        } else {
          // For sell section in sell direction, use long positions or selected position
          if (!state.isIndexTrading) {
            available = getAvailableToClose(positions).long.toDouble();
          } else {
            final restNum = state.selectedPositionShort?.restNum ?? 0;
            if (restNum > 0) {
              available = restNum;
            }
          }
        }
      }
    }
    emit(state.copyWith(availableQuantity: available));
    return available;
  }

  double getQuantityByFraction(OrderFraction? orderFraction, {TradeDirection? tradeDirection}) {
    try {
      final direction = tradeDirection ?? state.tradeDirection;
      if (direction == TradeDirection.buy) {
        /// 合约无需进行汇率转换
        bool contractSelected = state.contract != null;
        final quantity = _calculateTradingQuantity(
          accountBalance:
              contractSelected ? state.accountBalance : state.accountBalance * (state.exchangeRate?.rate ?? 1),
          fraction: orderFraction?.fraction ?? 0,
          direction: direction,
          stockPrice: state.stockInfo?.latestPrice ?? 0,
          tradeUnit: state.indexStockData?.tradeUnit,
        );
        return quantity;
      } else if (direction == TradeDirection.sell && state.maximumQuantityToSell > 0) {
        if (state.isIndexTrading) {
          double maxRestNum = max(state.selectedPositionLong?.restNum ?? 0, state.selectedPositionShort?.restNum ?? 0);
          return (maxRestNum * (orderFraction?.fraction ?? 0)).floorToDouble();
          // if((state.selectedPositionOpenShort?.restNum??0)>0){
          //   return
          // }else if ((state.selectedPositionSellLong?.restNum??0)>0){
          //
          // }
        } else {
          return (state.maximumQuantityToSell * (orderFraction?.fraction ?? 0)).floorToDouble();
        }
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  void setMaximumQuantityToSell(double maximumQuantityToSell) =>
      emit(state.copyWith(maximumQuantityToSell: maximumQuantityToSell));

  void setAccountBalance(double accountBalance) => emit(state.copyWith(accountBalance: accountBalance));

  void getKlineDetailList(String? instrument, KlineOption? option) async {
    if (instrument == null || option?.period == null) throw "Data error";
    emit(state.copyWith(klineDetailListStatus: DataStatus.loading, klineOption: option));
    final result = await MarketService().getKlineDetailList(instrument, option!.period, option.type);
    try {
      if (result.isSuccess) {
        emit(state.copyWith(klineDetailListStatus: DataStatus.success, klineDetailList: result.data));
      } else {
        emit(state.copyWith(klineDetailListStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(klineDetailListStatus: DataStatus.failed, error: e.toString()));
    }
  }

  /// Updates the kline detail list with new data from socket
  /// This method is used to update the kline data directly without making a new API call
  void updateKlineDetailList(StockKlineResponse updatedData) {
    emit(state.copyWith(
      klineDetailListStatus: DataStatus.success,
      klineDetailList: updatedData,
    ));
  }

  void getMarketStatus({required String market, required String symbol, required String securityType}) async {
    try {
      emit(state.copyWith(marketStatusFetchStatus: DataStatus.loading));
      final result = await MarketService().getMarketStatus(
        market: market,
        symbol: symbol,
        securityType: securityType,
      );

      if (!result.isSuccess || result.data == null) {
        throw Exception(result.error ?? "Failed to fetch market status");
      }

      emit(state.copyWith(
        marketStatusFetchStatus: DataStatus.success,
        marketStatus: result.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        marketStatusFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Determines if the trading quantity can be incremented by one lot size based on various conditions:
  /// - For buy orders:
  ///   - In index trading: Checks if new quantity exceeds available quantity
  ///   - In regular trading: Validates if account balance can cover increased quantity plus fees
  /// - For sell orders: Ensures new quantity doesn't exceed maximum sellable quantity
  ///
  /// Returns true if quantity can be incremented, false otherwise.
  bool canIncrementQuantity() {
    // Calculate new quantity after increment
    final quantity = state.quantity + state.lotSize;
    // Get current trade direction (buy/sell)
    final direction = state.tradeDirection;
    if (direction == TradeDirection.buy) {
      // Special handling for index trading
      if (state.isIndexTrading) {
        // Check if new quantity exceeds available quantity for index trading
        if (quantity > state.availableQuantity) {
          return false;
        }
        return true;
      }
      // Get current account balance
      final balance = state.accountBalance;
      // Determine stock price based on latest price or limit price
      final stockPrice = direction == TradeDirection.buy ? state.stockInfo?.latestPrice ?? 0 : state.limit;
      // Calculate trading fees
      final serviceCharge = calculateFee(balance);
      // Calculate total amount needed including fees based on CN rate
      final amount = ((quantity * stockPrice) + serviceCharge) / (state.exchangeRate?.rate ?? 1);
      if (amount > balance) {
        return false;
      }
    } else if (direction == TradeDirection.sell && state.maximumQuantityToSell > 0) {
      // For sell orders, check if new quantity exceeds maximum sellable quantity
      if (quantity > state.maximumQuantityToSell) {
        return false;
      }
    }
    return true;
  }

  bool canDecrementQuality() {
    final quantity = double.parse(state.quantity.toStringAsFixed(1));
    if (quantity <= state.lotSize) {
      return false;
    }
    return true;
  }

  /// Determines if the limit price can be decremented any further
  bool canDecrementLimit() {
    final limit = state.limit;
    if (limit <= 0.001) {
      return false;
    }
    return true;
  }

  bool isBuyingEnabled() {
    final balance = state.accountBalance;
    if (balance <= 0) {
      return false;
    }
    return true;
  }

  bool isSellingEnabled({bool isSellShort = false}) {
    if (isSellShort) {
      final balance = state.accountBalance;
      if (balance <= 0) {
        return false;
      }
      return true;
    }
    final quantity = state.maximumQuantityToSell;
    if (quantity <= 0) {
      return false;
    }
    return true;
  }

  void getCompanyInfo({required String market, required String symbol, required String securityType}) async {
    try {
      emit(state.copyWith(companyInfoFetchStatus: DataStatus.loading));
      final result = await MarketService().getCompanyInfo(
        market: market,
        symbol: symbol,
        securityType: securityType,
      );

      if (!result.isSuccess || result.data == null) {
        throw Exception(result.error ?? "Failed to fetch market status");
      }

      emit(state.copyWith(
        companyInfoFetchStatus: DataStatus.success,
        companyInfo: result.data,
      ));
    } catch (e) {
      emit(state.copyWith(
        companyInfoFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void getDistFlow({required String market, required String symbol, required String securityType}) async {
    try {
      emit(state.copyWith(distFlowFetchStatus: DataStatus.loading));
      final result = await MarketService().getDistFlow(
        market: market,
        symbol: symbol,
        securityType: securityType,
      );

      if (!result.isSuccess || result.data == null) {
        throw Exception(result.error ?? "Failed to fetch market status");
      }

      emit(state.copyWith(
        distFlowFetchStatus: DataStatus.success,
        distFlowResponse: result.data,
        updatedTime: DateTime.now(),
      ));
    } catch (e) {
      emit(state.copyWith(
        distFlowFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Updates the trade type and related UI state in the trading interface.
  ///
  /// This function:
  /// - Sets the [TradeTabType] (One of two tabs: Quotes or Trading)
  /// - Preserves the current stock information as a constant reference
  /// - Makes the bottom trading widget visible
  ///
  /// [tradeType] The type of trade to switch to (defined in TradeTabType)
  void setTradeType(TradeTabType tradeType) {
    emit(state.copyWith(tradeType: tradeType, stockInfoConstant: state.stockInfo));
    setBottomWidgetVisible(true);
  }

  void setStockWidgetCount(StockOrderLevelType stockWidgetCount) =>
      emit(state.copyWith(stockWidgetCount: stockWidgetCount));

  void cancelOrder(int orderId) async {
    emit(state.copyWith(orderCancelStatus: DataStatus.loading));
    final result = await _marketService.cancelOrder(orderId: orderId);
    try {
      if (result.isSuccess) {
        emit(state.copyWith(orderCancelStatus: DataStatus.success));
      } else {
        emit(state.copyWith(orderCancelStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(orderCancelStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> setExpireTime({required int positionId, required int timerValue}) async {
    emit(state.copyWith(setExpireTimeStatus: DataStatus.loading));
    final result = await _marketService.setExpireTime(positionId: positionId, timerValue: timerValue);
    try {
      if (result.isSuccess) {
        emit(state.copyWith(setExpireTimeStatus: DataStatus.success));
      } else {
        emit(state.copyWith(setExpireTimeStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(setExpireTimeStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void setIndexTrading({bool? isIndexTrading}) => emit(state.copyWith(
        isIndexTrading: isIndexTrading,
      ));

  Future<void> fetchTradeHandlingFeeConfig({
    required String market,
    required String securityType,
    String? chargePackageId,
  }) async {
    emit(state.copyWith(calculateConfigStatus: DataStatus.loading));
    final results = await Future.wait([
      TradeApi.fetchHandingFeeConfigList(
        market: market,
        direction: TradeDirection.buy.value,
        securityType: securityType,
        chargePackageId: chargePackageId,
      ),
      TradeApi.fetchHandingFeeConfigList(
        market: market,
        direction: TradeDirection.sell.value,
        securityType: securityType,
        chargePackageId: chargePackageId,
      )
    ]);
    if (results.every((res) => res.$1)) {
      emit(state.copyWith(
        calculateConfigStatus: DataStatus.success,
        calculateConfigBuy: results[0].$2,
        calculateConfigSell: results[1].$2,
      ));
    } else {
      emit(state.copyWith(
        calculateConfigStatus: DataStatus.failed,
      ));
    }
  }

  void setBottomWidgetVisible(bool isVisible) => emit(state.copyWith(isBottomWidgetVisible: isVisible));

  double get dealPrice => state.priceType == PriceType.market ? state.stockInfo?.latestPrice ?? 0 : state.limit;

  void setExchangeRate(ExchangeRate? exchangeRate) => emit(state.copyWith(exchangeRate: () => exchangeRate));

  void setSelectedPositionOpenShort(OrderRecord? position) =>
      emit(state.copyWith(selectedPositionOpenShort: () => position, quantity: position?.restNum));

  void setSelectedPositionSellLong(OrderRecord? position) =>
      emit(state.copyWith(selectedPositionSellLong: () => position, quantity: position?.restNum));

  void setSelectedPositionTimed(OrderRecord? position) =>
      emit(state.copyWith(selectedPositionTimed: () => position, quantity: position?.restNum));

  void setTradingType(String? tradingType) => emit(state.copyWith(tradingType: tradingType));

  void getBrokerQueue(String instrument) async {
    emit(state.copyWith(brokerQueueStatus: DataStatus.loading));
    final result = await _marketService.getBrokerQueue(instrument);
    try {
      if (result.isSuccess) {
        emit(state.copyWith(brokerQueueStatus: DataStatus.success, brokerQueue: result.data));
      } else {
        emit(state.copyWith(brokerQueueStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(brokerQueueStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> updateContract() async {
    if (state.contract?.id == null) return;
    final result = await _accountService.getCurrentContractSummary(state.contract!.id!);
    if (result.isSuccess) {
      emit(state.copyWith(contract: () => result.data));
    }
  }

  /// Refreshes the account balance from the AccountInfoCubit and updates the trading state.
  /// This method should be called after successful orders to ensure available quantity calculations
  /// reflect the updated account balance.
  Future<void> refreshAccountBalance() async {
    try {
      final accountInfoCubit = getIt<AccountInfoCubit>();
      await accountInfoCubit.getAccountInfo();
      final accountInfo = accountInfoCubit.state.accountInfo;

      if (accountInfo != null) {
        // Update account balance in trading state
        // For contract trading, keep the contract's useAmount, otherwise use usableCash
        final newBalance = state.contract?.useAmount ?? accountInfo.usableCash ?? 0;
        emit(state.copyWith(accountBalance: newBalance));
      }
    } catch (e) {
      debugPrint('Error refreshing account balance: $e');
    }
  }

  /// Resets the trade quantity to the full available amount and sets the order fraction to full.
  /// This method should be called after successful orders to reset the quantity input to show
  /// the updated available quantity based on the refreshed account balance.
  void resetQuantityToFullAvailable() {
    try {
      // Reset order fraction to full and recalculate quantity based on updated balance
      final quantity = getQuantityByFraction(OrderFraction.full);
      emit(state.copyWith(
        orderFraction: () => OrderFraction.full,
        quantity: quantity,
      ));
    } catch (e) {
      debugPrint('Error resetting quantity to full available: $e');
    }
  }

  void setIndexStockData({int? tradeUnit, double? lotSize, bool allowShortSell = true}) => emit(state.copyWith(
        indexStockData: (
          tradeUnit: tradeUnit,
          lotSize: lotSize,
          allowShortSell: allowShortSell,
        ),
      ));

  void getTickList({required String instrument, int limitPerPage = 40}) async {
    try {
      if (state.tickList == null) {
        emit(state.copyWith(tickListStatus: DataStatus.loading));
      }
      final result = await _marketService.getTickList(
        instrument: instrument,
        page: (state.tickList?.data?.current ?? 0) + 1,
        limitPerPage: limitPerPage,
      );

      if (!result.isSuccess || result.data == null) {
        throw Exception(result.error ?? "Failed to fetch tick list");
      }

      emit(state.copyWith(
        tickListStatus: DataStatus.success,
        tickList: state.tickList == null
            ? result.data
            : state.tickList!.copyWith(
                data: state.tickList!.data!.copyWith(
                  records: [...?state.tickList!.data!.records, ...?result.data!.data!.records],
                  current: result.data!.data!.current,
                  total: result.data!.data!.total,
                  hasNext: result.data!.data!.hasNext,
                ),
              ),
      ));
    } catch (e) {
      emit(state.copyWith(
        tickListStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getCompanyNews(Instrument instrument, {bool isRefresh = false}) async {
    if (isRefresh || state.companyNewsResponse == null) {
      emit(state.copyWith(companyNewsFetchStatus: DataStatus.loading));
    } else {
      if (state.companyNewsResponse?.data?.hasNext == false) return;
    }
    final result = await _marketService.getCompanyNews(
      instrument: instrument,
      page: isRefresh ? 1 : (state.companyNewsResponse?.data?.current ?? 0) + 1,
      limitPerPage: 20,
    );
    try {
      if (result.isSuccess) {
        emit(state.copyWith(
          companyNewsFetchStatus: DataStatus.success,
          companyNewsResponse: state.companyNewsResponse == null
              ? result.data
              : state.companyNewsResponse!.copyWith(
                  data: state.companyNewsResponse!.data!.copyWith(
                    records: [...?state.companyNewsResponse!.data!.records, ...?result.data!.data!.records],
                    current: result.data!.data!.current,
                    total: result.data!.data!.total,
                    hasNext: result.data!.data!.hasNext,
                  ),
                ),
        ));
      } else {
        emit(state.copyWith(
          companyNewsFetchStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        companyNewsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  // ==================== WebSocket Functions Start ====================
  // Functions for handling WebSocket subscriptions, message formatting, and real-time updates
  Future<void> subscribeToMarketSymbols({
    required Instrument instrument,
    List<String> actions = const ['Q', 'O', 'A', 'T'],
  }) async {
    // Create the symbols string following the same pattern
    final symbols =
        actions.map((e) => [instrument.market, instrument.securityType, e, instrument.symbol].join('|')).join(',');

    // Emit the subscription request
    _webSocketService.send({'action': SocketActions.subscribe, 'symbols': symbols});
  }

  Future<void> unsubscribeFromMarketSymbols({
    required Instrument instrument,
    List<String> actions = const ['Q', 'O', 'A', 'T'],
  }) async {
    final symbols =
        actions.map((e) => [instrument.market, instrument.securityType, e, instrument.symbol].join('|')).join(',');
    _webSocketService.send({'action': SocketActions.unsubscribe, 'symbols': symbols});
  }

  Ask _formatVolumeData(List<dynamic> list, int index) {
    final price = list[0];
    final no = list.length == 3 ? list[1] : '';
    final vol = list.length == 3 ? list[2] : list[1];

    return Ask(
      depthNo: index + 1,
      no: no is String ? int.tryParse(no) : 0,
      price: double.tryParse(price) ?? 0,
      vol: int.tryParse(vol) ?? 0,
    );
  }

  void _handleStockInfoUpdate(WebSocketMessage message) {
    if (state.stockInfo?.instrumentInfo == null) return;
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(state.stockInfo!.instrumentInfo.market);
    if (!isCurrentMarketOpen) return;
    final filteredMessage = message.withInstrument(state.stockInfo!.instrumentInfo);
    if (filteredMessage == null) return;

    final quoteData = filteredMessage.toQuoteData();
    if (quoteData == null) return;

    try {
      final newStockInfo = state.stockInfo!.copyWith(
        chg: quoteData.chg,
        latestPrice: quoteData.latestPrice,
        high: quoteData.high,
        low: quoteData.low,
        close: quoteData.close,
        volume: quoteData.volume,
        amount: quoteData.amount,
        gain: quoteData.gain,
      );
      emit(state.copyWith(stockInfo: newStockInfo));
    } catch (e) {
      debugPrint('Error updating stock info: $e');
    }
  }

  void _handleMarketDepthUpdate(WebSocketMessage message) {
    if (state.stockInfo?.instrumentInfo == null) return;
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(state.stockInfo!.instrumentInfo.market);
    if (!isCurrentMarketOpen) return;
    final filteredMessage = message.withInstrument(state.stockInfo!.instrumentInfo);
    if (filteredMessage == null) return;

    try {
      final data = filteredMessage.data['data'].toString().split('|');
      final depthData = data.sublist(4); // Skip first 4 elements
      final chunkSize = state.stockInfo?.instrumentInfo.market == 'US' ? 2 : 3;

      // Split data into chunks
      final List<List<String>> formatData = [];
      for (var i = 0; i < depthData.length; i += chunkSize) {
        formatData.add(depthData.sublist(
          i,
          i + chunkSize > depthData.length ? depthData.length : i + chunkSize,
        ));
      }

      // Split into ask and bid
      final halfLength = formatData.length ~/ 2;
      final ask = formatData.sublist(0, halfLength);
      final bid = formatData.sublist(halfLength);

      // Format and update market depth
      final formattedAsk = ask.asMap().entries.map((entry) => _formatVolumeData(entry.value, entry.key)).toList();
      final formattedBid = bid.asMap().entries.map((entry) => _formatVolumeData(entry.value, entry.key)).toList();

      final newMarketDepth = MarketDepthData(
        ask: formattedAsk,
        bid: formattedBid,
      );

      emit(state.copyWith(marketDepth: newMarketDepth));
    } catch (e) {
      debugPrint('Error processing market depth: $e');
    }
  }

  void _handleTickData(WebSocketMessage message) {
    if (state.stockInfoConstant?.instrumentInfo == null) return;
    final filteredMessage =
        message.withInstrument(Instrument(instrument: state.stockInfoConstant?.instrumentInfo.instrument ?? ''));
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(state.stockInfoConstant?.instrumentInfo.market);
    if (!isCurrentMarketOpen) return;
    if (filteredMessage == null) return;
    final [market, securityType, tradeTime, symbol, tradePrice, tradeVolume, direction] =
        filteredMessage.data['data'].split('|');
    if (isClosed) return;
    emit(
      state.copyWith(
        tickList: state.tickList?.copyWith(
          data: state.tickList?.data?.copyWith(
            records: [
              TickRecord(
                tradePrice: double.parse(tradePrice),
                tradeVolume: int.parse(tradeVolume),
                direction: direction,
                tradeTime: int.parse(tradeTime),
              ),
              ...?state.tickList?.data?.records,
            ],
          ),
        ),
      ),
    );
  }
// ==================== WebSocket Functions End ====================
}

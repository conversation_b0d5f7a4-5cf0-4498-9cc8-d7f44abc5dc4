import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/withdraw_config_entity.dart';

WithdrawConfigEntity $WithdrawConfigEntityFromJson(Map<String, dynamic> json) {
  final WithdrawConfigEntity withdrawConfigEntity = WithdrawConfigEntity();
  final WithdrawConfigEntityChannelName? channelName = jsonConvert.convert<WithdrawConfigEntityChannelName>(
      json['channelName']);
  if (channelName != null) {
    withdrawConfigEntity.channelName = channelName;
  }
  final String? endWithdrawalTime = jsonConvert.convert<String>(json['endWithdrawalTime']);
  if (endWithdrawalTime != null) {
    withdrawConfigEntity.endWithdrawalTime = endWithdrawalTime;
  }
  final int? handlingFeeType = jsonConvert.convert<int>(json['handlingFeeType']);
  if (handlingFeeType != null) {
    withdrawConfigEntity.handlingFeeType = handlingFeeType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    withdrawConfigEntity.id = id;
  }
  final int? maxWithdrawalAmount = jsonConvert.convert<int>(json['maxWithdrawalAmount']);
  if (maxWithdrawalAmount != null) {
    withdrawConfigEntity.maxWithdrawalAmount = maxWithdrawalAmount;
  }
  final int? minWithdrawalAmount = jsonConvert.convert<int>(json['minWithdrawalAmount']);
  if (minWithdrawalAmount != null) {
    withdrawConfigEntity.minWithdrawalAmount = minWithdrawalAmount;
  }
  final String? startWithdrawalTime = jsonConvert.convert<String>(json['startWithdrawalTime']);
  if (startWithdrawalTime != null) {
    withdrawConfigEntity.startWithdrawalTime = startWithdrawalTime;
  }
  final bool? testUsersCanWithdraw = jsonConvert.convert<bool>(json['testUsersCanWithdraw']);
  if (testUsersCanWithdraw != null) {
    withdrawConfigEntity.testUsersCanWithdraw = testUsersCanWithdraw;
  }
  final int? withdrawalFee = jsonConvert.convert<int>(json['withdrawalFee']);
  if (withdrawalFee != null) {
    withdrawConfigEntity.withdrawalFee = withdrawalFee;
  }
  final bool? withdrawalStatus = jsonConvert.convert<bool>(json['withdrawalStatus']);
  if (withdrawalStatus != null) {
    withdrawConfigEntity.withdrawalStatus = withdrawalStatus;
  }
  final int? withdrawalsDayCount = jsonConvert.convert<int>(json['withdrawalsDayCount']);
  if (withdrawalsDayCount != null) {
    withdrawConfigEntity.withdrawalsDayCount = withdrawalsDayCount;
  }
  return withdrawConfigEntity;
}

Map<String, dynamic> $WithdrawConfigEntityToJson(WithdrawConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['channelName'] = entity.channelName.toJson();
  data['endWithdrawalTime'] = entity.endWithdrawalTime;
  data['handlingFeeType'] = entity.handlingFeeType;
  data['id'] = entity.id;
  data['maxWithdrawalAmount'] = entity.maxWithdrawalAmount;
  data['minWithdrawalAmount'] = entity.minWithdrawalAmount;
  data['startWithdrawalTime'] = entity.startWithdrawalTime;
  data['testUsersCanWithdraw'] = entity.testUsersCanWithdraw;
  data['withdrawalFee'] = entity.withdrawalFee;
  data['withdrawalStatus'] = entity.withdrawalStatus;
  data['withdrawalsDayCount'] = entity.withdrawalsDayCount;
  return data;
}

extension WithdrawConfigEntityExtension on WithdrawConfigEntity {
  WithdrawConfigEntity copyWith({
    WithdrawConfigEntityChannelName? channelName,
    String? endWithdrawalTime,
    int? handlingFeeType,
    int? id,
    int? maxWithdrawalAmount,
    int? minWithdrawalAmount,
    String? startWithdrawalTime,
    bool? testUsersCanWithdraw,
    int? withdrawalFee,
    bool? withdrawalStatus,
    int? withdrawalsDayCount,
  }) {
    return WithdrawConfigEntity()
      ..channelName = channelName ?? this.channelName
      ..endWithdrawalTime = endWithdrawalTime ?? this.endWithdrawalTime
      ..handlingFeeType = handlingFeeType ?? this.handlingFeeType
      ..id = id ?? this.id
      ..maxWithdrawalAmount = maxWithdrawalAmount ?? this.maxWithdrawalAmount
      ..minWithdrawalAmount = minWithdrawalAmount ?? this.minWithdrawalAmount
      ..startWithdrawalTime = startWithdrawalTime ?? this.startWithdrawalTime
      ..testUsersCanWithdraw = testUsersCanWithdraw ?? this.testUsersCanWithdraw
      ..withdrawalFee = withdrawalFee ?? this.withdrawalFee
      ..withdrawalStatus = withdrawalStatus ?? this.withdrawalStatus
      ..withdrawalsDayCount = withdrawalsDayCount ?? this.withdrawalsDayCount;
  }
}

WithdrawConfigEntityChannelName $WithdrawConfigEntityChannelNameFromJson(Map<String, dynamic> json) {
  return WithdrawConfigEntityChannelName();
}

Map<String, dynamic> $WithdrawConfigEntityChannelNameToJson(WithdrawConfigEntityChannelName entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension WithdrawConfigEntityChannelNameExtension on WithdrawConfigEntityChannelName {
}